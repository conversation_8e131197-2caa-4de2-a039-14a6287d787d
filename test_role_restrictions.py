#!/usr/bin/env python3
"""
Тестовый скрипт для проверки ограничений доступа к ролям
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import check_role_access

def test_role_access():
    """Тестирование функции проверки доступа к ролям"""
    
    print("🧪 Тестирование ограничений доступа к ролям\n")
    
    # Тестовые случаи
    test_cases = [
        # (user_role, target_role, expected_result, description)
        ("admin", "admin", True, "Админ -> Админ"),
        ("admin", "manager", True, "Админ -> Менеджер"),
        ("admin", "curator", True, "Админ -> Куратор"),
        ("admin", "teacher", True, "Админ -> Преподаватель"),
        ("admin", "student", True, "Админ -> Студент"),
        
        ("manager", "admin", False, "Менеджер -> Админ"),
        ("manager", "manager", True, "Менеджер -> Менеджер"),
        ("manager", "curator", False, "Менеджер -> Куратор"),
        ("manager", "teacher", False, "Менеджер -> Преподаватель"),
        ("manager", "student", False, "Менеджер -> Студент"),
        
        ("curator", "admin", False, "Куратор -> Админ"),
        ("curator", "manager", False, "Куратор -> Менеджер"),
        ("curator", "curator", True, "Куратор -> Куратор"),
        ("curator", "teacher", False, "Куратор -> Преподаватель"),
        ("curator", "student", False, "Куратор -> Студент"),
        
        ("teacher", "admin", False, "Преподаватель -> Админ"),
        ("teacher", "manager", False, "Преподаватель -> Менеджер"),
        ("teacher", "curator", False, "Преподаватель -> Куратор"),
        ("teacher", "teacher", True, "Преподаватель -> Преподаватель"),
        ("teacher", "student", False, "Преподаватель -> Студент"),
        
        ("student", "admin", False, "Студент -> Админ"),
        ("student", "manager", False, "Студент -> Менеджер"),
        ("student", "curator", False, "Студент -> Куратор"),
        ("student", "teacher", False, "Студент -> Преподаватель"),
        ("student", "student", True, "Студент -> Студент"),
    ]
    
    passed = 0
    failed = 0
    
    for user_role, target_role, expected, description in test_cases:
        result = check_role_access(target_role, user_role)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        
        if result == expected:
            passed += 1
        else:
            failed += 1
            
        print(f"{status} | {description}: {result} (ожидалось: {expected})")
    
    print(f"\n📊 Результаты тестирования:")
    print(f"✅ Пройдено: {passed}")
    print(f"❌ Провалено: {failed}")
    print(f"📈 Общий результат: {passed}/{passed + failed}")
    
    if failed == 0:
        print("\n🎉 Все тесты пройдены успешно!")
        return True
    else:
        print(f"\n⚠️ Обнаружены проблемы в {failed} тестах")
        return False

def test_role_scenarios():
    """Тестирование реальных сценариев использования"""
    
    print("\n🎭 Тестирование реальных сценариев:\n")
    
    scenarios = [
        {
            "name": "Админ может зайти в любое меню",
            "user_role": "admin",
            "commands": ["/admin", "/manager", "/curator", "/teacher", "/student"],
            "expected_access": [True, True, True, True, True]
        },
        {
            "name": "Менеджер может зайти только в свое меню",
            "user_role": "manager", 
            "commands": ["/admin", "/manager", "/curator", "/teacher", "/student"],
            "expected_access": [False, True, False, False, False]
        },
        {
            "name": "Куратор может зайти только в свое меню",
            "user_role": "curator",
            "commands": ["/admin", "/manager", "/curator", "/teacher", "/student"],
            "expected_access": [False, False, True, False, False]
        },
        {
            "name": "Преподаватель может зайти только в свое меню",
            "user_role": "teacher",
            "commands": ["/admin", "/manager", "/curator", "/teacher", "/student"],
            "expected_access": [False, False, False, True, False]
        },
        {
            "name": "Студент может зайти только в свое меню",
            "user_role": "student",
            "commands": ["/admin", "/manager", "/curator", "/teacher", "/student"],
            "expected_access": [False, False, False, False, True]
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"📋 {scenario['name']}")
        scenario_passed = True
        
        for i, command in enumerate(scenario['commands']):
            target_role = command[1:]  # убираем '/'
            expected = scenario['expected_access'][i]
            result = check_role_access(target_role, scenario['user_role'])
            
            status = "✅" if result == expected else "❌"
            access_text = "ДОСТУП" if result else "ЗАПРЕТ"
            
            if result != expected:
                scenario_passed = False
                all_passed = False
            
            print(f"  {status} {command}: {access_text}")
        
        print(f"  {'✅ Сценарий пройден' if scenario_passed else '❌ Сценарий провален'}\n")
    
    return all_passed

if __name__ == "__main__":
    print("=" * 60)
    print("🔒 ТЕСТИРОВАНИЕ СИСТЕМЫ ОГРАНИЧЕНИЙ ДОСТУПА К РОЛЯМ")
    print("=" * 60)
    
    # Запускаем тесты
    basic_tests_passed = test_role_access()
    scenario_tests_passed = test_role_scenarios()
    
    print("=" * 60)
    if basic_tests_passed and scenario_tests_passed:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
        print("✅ Система ограничений доступа работает корректно")
    else:
        print("❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ В ТЕСТАХ")
        print("⚠️ Требуется проверка системы ограничений")
    print("=" * 60)
