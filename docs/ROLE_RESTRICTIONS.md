# Система ограничений доступа к ролям и обработки новых пользователей

## Описание

Реализована система ограничений доступа к командам ролей и умная обработка новых пользователей. Убран хардкод админа, команды ролей доступны только админам через фильтры.

## Изменения

### 1. Убран хардкод админа

Удален хардкод админа из `middlewares/role_middleware.py`. Теперь роли определяются только из базы данных.

### 2. Д<PERSON><PERSON><PERSON>влена роль "new_user"

Пользователи, не найденные в базе данных, получают роль `"new_user"` и видят специальное предупреждение.

### 3. AdminFilter для команд ролей

Создан фильтр `AdminFilter` в `main.py`:

```python
class AdminFilter(BaseFilter):
    async def __call__(self, message, user_role: str) -> bool:
        return user_role == "admin"
```

### 4. Условная регистрация команд

Команды ролей регистрируются только для админов:

```python
admin_filter = AdminFilter()

dp.message.register(admin_command, Command("admin"), admin_filter)
dp.message.register(manager_command, Command("manager"), admin_filter)
# и т.д.
```

### 5. Обработчик новых пользователей

Создан модуль `common/new_user_handler.py` с умным предупреждением для новых пользователей.

## Правила доступа

| Роль пользователя | /admin | /manager | /curator | /teacher | /student |
|-------------------|--------|----------|----------|----------|----------|
| **admin**         | ✅     | ✅       | ✅       | ✅       | ✅       |
| **manager**       | ❌     | ✅       | ❌       | ❌       | ❌       |
| **curator**       | ❌     | ❌       | ✅       | ❌       | ❌       |
| **teacher**       | ❌     | ❌       | ❌       | ✅       | ❌       |
| **student**       | ❌     | ❌       | ❌       | ❌       | ✅       |

## Поведение при нарушении доступа

При попытке доступа к недоступной роли пользователь получает сообщение:

```
❌ У вас нет доступа к меню роли 'target_role'.
Ваша роль: user_role
```

## Команда /start

Команда `/start` по-прежнему автоматически перенаправляет пользователя в меню его роли без возможности выбора.

## Безопасность

- Проверка ролей происходит на уровне middleware (RoleMiddleware)
- Дополнительная проверка на уровне команд предотвращает несанкционированный доступ
- Админ сохраняет полный доступ для администрирования системы
- Все остальные роли изолированы друг от друга

## Тестирование

Система была протестирована на всех возможных комбинациях ролей и команд. Все тесты прошли успешно.
