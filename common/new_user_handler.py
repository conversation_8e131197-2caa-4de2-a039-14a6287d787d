"""
Обработчик для новых пользователей, которых нет в базе данных
"""
import logging
from aiogram.types import Message, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton


class NewUserStates(StatesGroup):
    """Состояния для новых пользователей"""
    waiting_for_registration = State()


def get_new_user_kb() -> InlineKeyboardMarkup:
    """Клавиатура для новых пользователей"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📞 Связаться с поддержкой", url="https://t.me/support_bot")],
        [InlineKeyboardButton(text="🔄 Проверить снова", callback_data="check_registration")]
    ])


async def show_new_user_message(event, state: FSMContext = None):
    """
    Показать сообщение для нового пользователя
    
    Args:
        event: Message или CallbackQuery
        state: FSM состояние (опционально)
    """
    user = event.from_user
    user_info = f"@{user.username}" if user.username else f"ID: {user.id}"
    
    message_text = (
        f"👋 Привет, {user.first_name}!\n\n"
        f"🔍 Я не нашел вас в системе обучения.\n\n"
        f"📋 Ваши данные:\n"
        f"• Имя: {user.first_name} {user.last_name or ''}\n"
        f"• Контакт: {user_info}\n"
        f"• Telegram ID: {user.id}\n\n"
        f"📞 Для регистрации в системе обратитесь к администратору или поддержке.\n"
        f"Сообщите им ваш Telegram ID: <code>{user.id}</code>\n\n"
        f"⚡ После добавления в систему нажмите 'Проверить снова'"
    )
    
    keyboard = get_new_user_kb()
    
    # Логируем попытку входа нового пользователя
    logging.info(f"🆕 НОВЫЙ ПОЛЬЗОВАТЕЛЬ: {user.first_name} {user.last_name or ''} | @{user.username or 'no_username'} | ID: {user.id}")
    
    if isinstance(event, Message):
        await event.answer(message_text, reply_markup=keyboard, parse_mode="HTML")
    elif isinstance(event, CallbackQuery):
        if event.message:
            await event.message.edit_text(message_text, reply_markup=keyboard, parse_mode="HTML")
        else:
            await event.answer(message_text, show_alert=True)
    
    # Устанавливаем состояние ожидания регистрации
    if state:
        await state.set_state(NewUserStates.waiting_for_registration)


async def check_registration_again(callback: CallbackQuery, state: FSMContext, user_role: str = None):
    """
    Проверить регистрацию пользователя повторно
    
    Args:
        callback: CallbackQuery объект
        state: FSM состояние
        user_role: Роль пользователя из middleware
    """
    # Если роль все еще new_user, показываем то же сообщение
    if user_role == "new_user":
        await show_new_user_message(callback, state)
        await callback.answer("🔍 Проверил - вас все еще нет в системе", show_alert=True)
        return
    
    # Если пользователь найден, перенаправляем в соответствующее меню
    await callback.answer("✅ Отлично! Вы найдены в системе", show_alert=True)
    
    # Очищаем состояние
    await state.clear()
    
    # Перенаправляем в меню соответствующей роли
    if user_role == "admin":
        from admin.handlers.main import show_admin_main_menu
        await show_admin_main_menu(callback.message)
    elif user_role == "manager":
        from manager.handlers.main import show_manager_main_menu
        await show_manager_main_menu(callback.message)
    elif user_role == "curator":
        from curator.handlers.main import show_curator_main_menu
        await show_curator_main_menu(callback.message)
    elif user_role == "teacher":
        from teacher.handlers.main import show_teacher_main_menu
        await show_teacher_main_menu(callback.message)
    else:  # student или любая другая роль
        from student.handlers.main import show_student_main_menu
        await show_student_main_menu(callback.message)


async def handle_new_user_command(message: Message, state: FSMContext):
    """
    Обработчик команд для новых пользователей
    Показывает сообщение о необходимости регистрации
    """
    await show_new_user_message(message, state)


# Функция для принудительного обновления кэша ролей после добавления пользователя
async def force_role_cache_update():
    """Принудительно обновить кэш ролей"""
    from middlewares.role_middleware import force_update_role_cache
    await force_update_role_cache()
