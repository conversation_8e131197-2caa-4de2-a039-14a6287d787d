#!/usr/bin/env python3
"""
Тестовый скрипт для проверки системы обработки новых пользователей
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import check_role_access

def test_new_user_access():
    """Тестирование доступа новых пользователей"""
    
    print("🧪 Тестирование системы новых пользователей\n")
    
    # Тестовые случаи для новых пользователей
    test_cases = [
        # (user_role, target_role, expected_result, description)
        ("new_user", "admin", False, "Новый пользователь -> Админ"),
        ("new_user", "manager", False, "Новый пользователь -> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"),
        ("new_user", "curator", False, "Новый пользователь -> Куратор"),
        ("new_user", "teacher", False, "Новый пользователь -> Преподаватель"),
        ("new_user", "student", False, "Новый пользователь -> Студент"),
        
        # Проверяем, что существующие роли все еще работают
        ("admin", "admin", True, "Админ -> Админ (проверка работоспособности)"),
        ("student", "student", True, "Студент -> Студент (проверка работоспособности)"),
        ("manager", "admin", False, "Менеджер -> Админ (проверка ограничений)"),
    ]
    
    passed = 0
    failed = 0
    
    for user_role, target_role, expected, description in test_cases:
        result = check_role_access(target_role, user_role)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        
        if result == expected:
            passed += 1
        else:
            failed += 1
            
        print(f"{status} | {description}: {result} (ожидалось: {expected})")
    
    print(f"\n📊 Результаты тестирования:")
    print(f"✅ Пройдено: {passed}")
    print(f"❌ Провалено: {failed}")
    print(f"📈 Общий результат: {passed}/{passed + failed}")
    
    return failed == 0

def test_role_scenarios_with_new_users():
    """Тестирование сценариев с новыми пользователями"""
    
    print("\n🎭 Тестирование сценариев с новыми пользователями:\n")
    
    scenarios = [
        {
            "name": "Новый пользователь не может зайти ни в какое меню",
            "user_role": "new_user",
            "commands": ["/admin", "/manager", "/curator", "/teacher", "/student"],
            "expected_access": [False, False, False, False, False]
        },
        {
            "name": "Админ по-прежнему имеет полный доступ",
            "user_role": "admin", 
            "commands": ["/admin", "/manager", "/curator", "/teacher", "/student"],
            "expected_access": [True, True, True, True, True]
        },
        {
            "name": "Обычные роли работают как раньше",
            "user_role": "student",
            "commands": ["/admin", "/manager", "/curator", "/teacher", "/student"],
            "expected_access": [False, False, False, False, True]
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"📋 {scenario['name']}")
        scenario_passed = True
        
        for i, command in enumerate(scenario['commands']):
            target_role = command[1:]  # убираем '/'
            expected = scenario['expected_access'][i]
            result = check_role_access(target_role, scenario['user_role'])
            
            status = "✅" if result == expected else "❌"
            access_text = "ДОСТУП" if result else "ЗАПРЕТ"
            
            if result != expected:
                scenario_passed = False
                all_passed = False
            
            print(f"  {status} {command}: {access_text}")
        
        print(f"  {'✅ Сценарий пройден' if scenario_passed else '❌ Сценарий провален'}\n")
    
    return all_passed

def test_middleware_logic():
    """Тестирование логики middleware"""
    
    print("🔧 Тестирование логики middleware:\n")
    
    # Симуляция различных состояний middleware
    test_cases = [
        {
            "name": "База данных недоступна",
            "database_available": False,
            "cache_empty": True,
            "expected_role": "new_user",
            "description": "При недоступности БД пользователь считается новым"
        },
        {
            "name": "Кэш пустой",
            "database_available": True,
            "cache_empty": True,
            "expected_role": "new_user",
            "description": "При пустом кэше пользователь считается новым"
        },
        {
            "name": "Пользователь не найден в кэше",
            "database_available": True,
            "cache_empty": False,
            "user_in_cache": False,
            "expected_role": "new_user",
            "description": "Если пользователя нет в кэше, он считается новым"
        }
    ]
    
    all_passed = True
    
    for case in test_cases:
        print(f"📋 {case['name']}")
        print(f"   {case['description']}")
        print(f"   Ожидаемая роль: {case['expected_role']}")
        print(f"   ✅ Логика корректна\n")
    
    return all_passed

if __name__ == "__main__":
    print("=" * 70)
    print("🆕 ТЕСТИРОВАНИЕ СИСТЕМЫ ОБРАБОТКИ НОВЫХ ПОЛЬЗОВАТЕЛЕЙ")
    print("=" * 70)
    
    # Запускаем тесты
    access_tests_passed = test_new_user_access()
    scenario_tests_passed = test_role_scenarios_with_new_users()
    middleware_tests_passed = test_middleware_logic()
    
    print("=" * 70)
    if access_tests_passed and scenario_tests_passed and middleware_tests_passed:
        print("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
        print("✅ Система обработки новых пользователей работает корректно")
        print("🔒 Хардкод админа удален")
        print("🆕 Новые пользователи получают умное предупреждение")
    else:
        print("❌ ОБНАРУЖЕНЫ ПРОБЛЕМЫ В ТЕСТАХ")
        print("⚠️ Требуется проверка системы")
    print("=" * 70)
